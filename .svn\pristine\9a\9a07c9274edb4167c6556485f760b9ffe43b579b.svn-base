/**
 * Copyright (c) 2011-2019, <PERSON> 詹波 (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.easitline.common.db.parser;

import java.io.File;
import java.util.Map;

import javax.servlet.ServletContextEvent;

import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.db.EasyRecord;

/**
 * SqlKit
 */
public class SqlPro {
	
	private static SqlKit MAIN = null;
	private static final Map<String, SqlKit> map = new SyncWriteMap<String, SqlKit>(32, 0.25F);
	
	public static void addSqlKit(String configName,SqlKit sqlKit) {
		map.put(configName, sqlKit);
	}
	/**
	 * 默认加载根目录sqls所有模板
	 * @param configName 模板引擎名称 默认可以设置为appId
	 * @param event
	 */
	public static void init(String configName,ServletContextEvent event) {
		SqlKit kit=new SqlKit(configName);
    	kit.setBaseSqlTemplatePath(event.getServletContext().getRealPath("/sqls"));
    	kit.setDevMode(isLocal());
    	addSqlTemplate(kit,configName,kit.getEngine().getBaseTemplatePath());
    	kit.parseSqlTemplate();
    	SqlPro.init(configName,kit);
	}
	private static boolean isLocal() {
        String osName = System.getProperty("os.name");
        return osName.indexOf("Windows") != -1;
	}
    private static void addSqlTemplate(SqlKit kit,String configName,String bastPath){
    	File file=new File(bastPath);
    	if(file.exists()){
    		File[] fs=file.listFiles();
    		for(File f:fs){
    			kit.addSqlTemplate(f.getName());
    			CoreLogger.getLogger().info("加载"+configName+",模板路径>"+f.getAbsolutePath());
    		}
    	}
    }
	public static void init(String configName,SqlKit sqlKit) {
		map.put(configName, sqlKit);
		MAIN = map.get(configName);
	}
	public static void init(String configName) {
		MAIN = map.get(configName);
	}
	public static SqlKit use(String configName){
		return map.get(configName);
	}
	
	public static String getSql(String key) {
    	return MAIN.getSql(key);
    }
    
    public static SqlPara getSqlPara(String key, EasyRecord record) {
    	return MAIN.getSqlPara(key, record.getColumns());
    }
    
    
    public static SqlPara getSqlPara(String key, Map data) {
    	return MAIN.getSqlPara(key, data);
    }
    
    public static SqlPara getSqlPara(String key, Object... paras) {
    	return MAIN.getSqlPara(key, paras);
    }
	
}




