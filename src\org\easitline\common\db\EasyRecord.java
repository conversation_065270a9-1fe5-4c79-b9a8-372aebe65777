package org.easitline.common.db;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;

/**
 * 
 * <AUTHOR>
 * Mars平台 数据库表字段封装类
 */
public class EasyRecord extends JSONObject implements Serializable{

	private static final long serialVersionUID = 905784513600884082L;
	
	public EasyRecord(){}
	
	private String whereSql;
	
	private String[] nullKeys = null;
	
	
	/**
	 * 表名
	 */
	private String tableName;
	/**
	 * 主键
	 */
	private String[] primaryKeys;
	
	private EasySQL easySQL;
	
	private boolean sortField=false;
	
	/**
	 * 数据库名称 默认不需赋值
	 */
	private String dbName;
	
	public EasyRecord(String tableName){
		this.tableName = tableName;
	}
	public EasyRecord(String tableName,String... primaryKeys){
		this.tableName = tableName;
		this.primaryKeys = primaryKeys;
	}
	public void setTableInfo(String tableName,String... primaryKeys){
		this.setTableName(tableName);
		this.setPrimaryKeys(primaryKeys);
	}
	public EasyRecord(String tableName,String[] primaryKeys,Object... idValues){
		this.tableName = tableName;
		this.primaryKeys = primaryKeys;
		String[] pKeys = getPrimaryKeys();
		Object[] ids = idValues;
		for(int i=0;i<pKeys.length;i++){
			if(ids==null){
				new Exception("主键的值不能为空!");
			}else{
				set(pKeys[i],ids[i]);
			}
		}
	}
	
	public String getTableName() {
		if(StringUtils.notBlank(getDbName())){
			return this.getDbName()+"."+tableName;
		}
		return tableName;
	}

	public String[] getPrimaryKeys() {
		return primaryKeys;
	}
	public String getPrimaryKey() {
		return primaryKeys[0];
	}
	public Object[] getPrimaryValues() {
		String[] pKeys = getPrimaryKeys();
		Object[] ids = new Object[pKeys.length];
		for(int i=0;i<ids.length;i++){
			ids[i]=get(pKeys[i]);
		}
		return ids;
	}
	public Object getPrimaryValue() {
		return get(getPrimaryKey());
	}

	public void setNull(String... keys) {
		this.nullKeys = keys;
	}
	
	public String[] getNullKeys() {
		return nullKeys;
	}
	
	public Map<String, Object> getColumns() {
		if(!sortField){
			return this;
		}
		Object[] key_arr = this.keySet().toArray();   
		JSONObject jsonObject=new JSONObject(true);
		Arrays.sort(key_arr);   
		for  (Object key : key_arr) {   
		     Object value = this.get(key);   
		     jsonObject.put(key.toString(), value);
		}
		return jsonObject;
	}
	public void setEasySQL(EasySQL easySQL){
		this.easySQL=easySQL;
	}
	
	public EasySQL getEasySQL(){
		return this.easySQL;
	}
	/**
	 * Set columns value with map.
	 * @param columns the columns map
	 */
	public EasyRecord setColumns(Map<String, Object> columns) {
		Map<String, Object> cols = new HashMap<String, Object>();
		for(Entry<String, Object> entry : columns.entrySet()){
			if(StringUtils.notBlank(entry.getKey())){
				cols.put(entry.getKey().toUpperCase(), entry.getValue());
			}
		}
		super.putAll(cols);
		return this;
	}
	
   public enum DataTypes {
		String(1), Integer(2), Float(3), Long(4),Timestamp(5),Double(6);
		@SuppressWarnings("unused")
		private int dataType;
	    private DataTypes(int dataType) {
	       this.dataType = dataType;
	    }
	}
	
	public EasyRecord setDataTypes(DataTypes dataType,String... fieldName) {
		if (fieldName != null) {
			for (int i = 0,len = fieldName.length; i < len; i++) {
				String key = fieldName[i].toUpperCase();
				if(dataType==DataTypes.Integer) {
					this.put(key, this.getIntValue(key));
				}else if(dataType==DataTypes.Long) {
					this.put(key, this.getLong(key));
				}else if(dataType==DataTypes.Double) {
					this.put(key, this.getDouble(key));
				}else {
					this.put(key, this.getString(key));
				}
			}
		}
		return this;
	}
	
	public boolean isPrimaryKey(String colName, String[] pKeys) {
		if(pKeys!=null){
			for (String pKey : pKeys) {
				if (colName.equalsIgnoreCase(pKey)) {
					return true;
				}
			}
		}
		return false;
	}
	
	public void setSQL(StringBuffer sql, List<Object> paras){
		String[] pKeys=this.getPrimaryKeys();
		sql.append(" ");
		for (Entry<String, Object> e: this.getColumns().entrySet()) {
			String colName = e.getKey();
			if (!isPrimaryKey(colName, pKeys)) {
				if (paras.size() > 0) {
					sql.append(", ");
				}
				sql.append("").append(colName).append(" = ? ");
				paras.add(e.getValue());
			}
		}
	}
	
	/**
	 * 设置主键值
	 * @param idValues 主键值
	 */
	public EasyRecord setPrimaryValues(Object... idValues) {
		String[] pKeys = getPrimaryKeys();
		Object[] ids = idValues;
		for(int i=0;i<pKeys.length;i++){
			if(ids==null){
				new Exception("主键的值不能为空!");
			}else{
				set(pKeys[i],ids[i]);
			}
		}
		return this;
	}
	public EasyRecord remove(String column) {
		super.remove(column);
		return this;
	}
	
	/**
	 * Remove columns of this record.
	 * @param columns the column names of the record
	 */
	public EasyRecord remove(String... columns) {
		if (columns != null)
			for (String c : columns)
				super.remove(c);
		return this;
	}
	/**
	 * Remove columns if it is null.
	 */
	public EasyRecord removeNullValueColumns() {
		for (java.util.Iterator<Entry<String, Object>> it = super.entrySet().iterator(); it.hasNext();) {
			Entry<String, Object> e = it.next();
			if (e.getValue() == null) {
				it.remove();
			}
		}
		return this;
	}
	/**
	 * Set column to record.
	 * @param column the column name
	 * @param value the value of the column
	 */
	public EasyRecord set(String column, Object value) {
		super.put(StringUtils.trimToEmpty(column).toUpperCase(), value);
		return this;
	}
	
	/**
	 * example: and state = xx and name <> 'xxx'
	 * if you set primaryKeys,nend to add 'and'
	 * @param whereSql
	 */
	public void setCondition(String whereSql) {
		this.whereSql=whereSql;
	}
	
	public String getCondition() {
		return whereSql;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public void setPrimaryKeys(String... primaryKeys) {
		this.primaryKeys = primaryKeys;
	}
	public void setPrimaryKey(String primaryKey) {
		this.primaryKeys =new String[]{primaryKey};
	}
	public String getDbName() {
		return dbName;
	}
	public EasyRecord setDbName(String dbName) {
		this.dbName = dbName;
		return this;
	}
	/**
	 * 字段排序后显示（ ASC）
	 */
	public void sortField(){
		this.sortField=true;
	}
}
