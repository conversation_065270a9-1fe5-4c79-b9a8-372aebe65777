package org.easitline.common.db.log;



import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
  
/**
 * Easitline jdbc 错误日志
 * <AUTHOR>
 *
 */
public class JDBCErrorLogger  {

	private static final   String APP_NAME = "easitline-jdbc";
	
	private static final   String LOG_NAME = "easitline-jdbc-error";
	
	public static Logger getLogger(){
		return LogEngine.getLogger(APP_NAME,LOG_NAME);
	}
}
