package org.easitline.common.db.impl;

import java.sql.ResultSet;

import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.log.JDBCLogger;
import org.easitline.common.utils.string.StringUtils;

public class EasyRowMapperImpl implements EasyRowMapper<EasyRow> {
	
	@SuppressWarnings("unchecked")
	@Override
	public EasyRow mapRow(ResultSet rs,int convertField) {
		try {
			java.sql.ResultSetMetaData meta = rs.getMetaData();
			// 取得列数
			int columnCount = meta.getColumnCount();
			// 保存列相关的信息
			String[] column = new String[columnCount];

			for (int i = 0; i < columnCount; i++) {
				if(convertField==2){
					column[i] = meta.getColumnLabel(i + 1).toUpperCase(); // 取得每一列的列名
				}else if(convertField==1){
					column[i] = meta.getColumnLabel(i + 1).toLowerCase();
				}else if(convertField==3) {
					column[i] = StringUtils.toCamelCase(meta.getColumnLabel(i + 1));
				}else{
					column[i] = meta.getColumnLabel(i + 1);
				}
				if (column[i] == null || column[i].trim().equals("")) {
					column[i] = "_COLUMN_" + (i + 1);
				}
			}

			EasyRowImpl easyRow = new EasyRowImpl(column);
			for (int i = 0; i < columnCount; i++) {
				easyRow.setColumnValue(i + 1, rs.getString(i + 1));
			}

			return easyRow;
		} catch (Exception ex) {
			JDBCLogger.getLogger().error("EasyRowMapperImpl.mapRow() exception , cause:"+ex.getMessage(),ex);
		}
		return null;
	}

}
