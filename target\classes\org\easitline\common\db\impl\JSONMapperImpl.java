package org.easitline.common.db.impl;

import java.sql.ResultSet;
import java.sql.Types;

import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.log.JDBCLogger;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;

public class JSO<PERSON>MapperImpl implements EasyRowMapper<JSONObject> {

	@SuppressWarnings("unchecked")
	@Override
	public JSONObject mapRow(ResultSet rs,int convertField) {
		try {
			java.sql.ResultSetMetaData meta = rs.getMetaData();
			// 取得列数
			int columnCount = meta.getColumnCount();
			// 保存列相关的信息
			String[] column = new String[columnCount];

			for (int i = 0; i < columnCount; i++) {
				if(convertField==2){
					column[i] = meta.getColumnLabel(i + 1).toUpperCase(); // 取得每一列的列名
				}else if(convertField==1){
					column[i] = meta.getColumnLabel(i + 1).toLowerCase();
				}else if(convertField==3) {
					column[i] = StringUtils.toCamelCase(meta.getColumnLabel(i + 1));
				}else{
					column[i] = meta.getColumnLabel(i + 1);
				}
			}
			JSONObject row = new JSONObject(true);
			for (int i = 0; i < columnCount; i++) {
				Object value = null;
				int columnType = meta.getColumnType(i + 1);
				
				switch (columnType) {
					case Types.INTEGER:
					case Types.SMALLINT:
					case Types.TINYINT:
						value = rs.getInt(i + 1);
						break;
					case Types.BIGINT:
						value = rs.getLong(i + 1);
						break;
					case Types.DECIMAL:
					case Types.NUMERIC:
						value = rs.getBigDecimal(i + 1);
						break;
					case Types.FLOAT:
					case Types.REAL:
						value = rs.getFloat(i + 1);
						break;
					case Types.DOUBLE:
						value = rs.getDouble(i + 1);
						break;
					case Types.BOOLEAN:
					case Types.BIT:
						value = rs.getBoolean(i + 1);
						break;
					case Types.TIMESTAMP:
						value = rs.getTimestamp(i + 1);
						break;
					case Types.DATE:
						value = rs.getDate(i + 1);
						break;
					case Types.TIME:
						value = rs.getTime(i + 1);
						break;
					default:
						value = rs.getString(i + 1);
				}
				
				if( value == null || rs.wasNull() || "null".equals(value.toString())){
					value = "";
				}
				row.put(column[i], value);
			}
			return row;
		} catch (Exception ex) {
			JDBCLogger.getLogger().error("JSONMapperImpl.mapRow() exception , cause:"+ex.getMessage(),ex);
		}
		return null;
	}

}
