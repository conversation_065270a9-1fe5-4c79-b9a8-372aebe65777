package org.easitline.common.db;

import java.io.Serializable;
import java.sql.SQLException;
import java.util.Map;

public abstract class EasyModel extends EasyRecord implements Serializable{
	private static final long serialVersionUID = 1L;
	
	protected abstract String getAppName();

	protected abstract String getAppDatasourceName();
	
	protected  EasyQuery getQuery(){
		if("default-ds".equals(getAppDatasourceName())){
			return EasyQuery.getQuery(this.getAppDatasourceName());
		}else{
    		return EasyQuery.getQuery(this.getAppName(),this.getAppDatasourceName());
		}
	}
	public Map<String, String> findById() throws SQLException{
		return this.getQuery().findById(this);
	}
	public Map<String, String> findById(Object... id) throws SQLException{
		this.setPrimaryValues(id);
		return this.getQuery().findById(this);
	}
	public boolean save()throws SQLException{
		return this.getQuery().save(this);
	}
	public boolean update()throws SQLException{
		return this.getQuery().update(this);
	}
	public boolean delete()throws SQLException{
		return this.getQuery().deleteById(this);
	}
	public boolean deleteById(Object... id)throws SQLException{
		this.setPrimaryValues(id);
		return this.getQuery().deleteById(this);
   }
	
}
