package org.easitline.common.db;

import java.sql.SQLException;
import java.io.InputStream;
import java.io.Reader;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Array;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.Date;
import java.sql.ParameterMetaData;
import java.sql.Ref;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Calendar;

import org.easitline.common.core.log.impl.CoreLogger;

import java.sql.Connection;
import java.sql.SQLWarning;

public class EasyPreparedStatement {
	private java.sql.PreparedStatement stmt;

	public EasyPreparedStatement(java.sql.PreparedStatement stmt) {
		this.stmt = stmt;
	}

	public ResultSet executeQuery() throws SQLException {
		return stmt.executeQuery();
	}

	public int executeUpdate() throws SQLException {
		return stmt.executeUpdate();
	}

	public void setNull(int parameterIndex, int sqlType) throws SQLException {
		stmt.setNull(parameterIndex, sqlType);
	}

	public void setBoolean(int parameterIndex, boolean x) throws SQLException {
		this.stmt.setBoolean(parameterIndex, x);
	}

	public void setByte(int parameterIndex, byte x) throws SQLException {
		this.stmt.setByte(parameterIndex, x);
	}

	public void setShort(int parameterIndex, short x) throws SQLException {
		this.stmt.setShort(parameterIndex, x);
	}

	public void setInt(int parameterIndex, int x) throws SQLException {
		this.stmt.setInt(parameterIndex, x);
	}

	public void setLong(int parameterIndex, long x) throws SQLException {
		this.stmt.setLong(parameterIndex, x);
	}

	public void setFloat(int parameterIndex, float x) throws SQLException {
		this.stmt.setFloat(parameterIndex, x);
	}

	public void setDouble(int parameterIndex, double x) throws SQLException {
		this.stmt.setDouble(parameterIndex, x);
	}

	public void setBigDecimal(int parameterIndex, BigDecimal x) throws SQLException {
		this.stmt.setBigDecimal(parameterIndex, x);
	}

	public void setString(int parameterIndex, String x) throws SQLException {
		this.stmt.setString(parameterIndex, x);
	}

	public void setBytes(int parameterIndex, byte[] x) throws SQLException {
		this.stmt.setBytes(parameterIndex, x);
	}

	public void setDate(int parameterIndex, Date x) throws SQLException {
		this.stmt.setDate(parameterIndex, x);
	}

	public void setTime(int parameterIndex, Time x) throws SQLException {
		this.stmt.setTime(parameterIndex, x);
	}

	public void setTimestamp(int parameterIndex, Timestamp x) throws SQLException {
		this.stmt.setTimestamp(parameterIndex, x);
	}

	public void setAsciiStream(int parameterIndex, InputStream x, int length) throws SQLException {
		this.stmt.setAsciiStream(parameterIndex, x, length);
	}

	public void setUnicodeStream(int parameterIndex, InputStream x, int length) throws SQLException {
		this.stmt.setUnicodeStream(parameterIndex, x, length);
	}

	public void setBinaryStream(int parameterIndex, InputStream x, int length) throws SQLException {
		this.stmt.setBinaryStream(parameterIndex, x, length);
	}

	public void clearParameters() throws SQLException {
		this.stmt.clearParameters();
	}

	public void setObject(int parameterIndex, Object x, int targetSqlType, int scale) throws SQLException {
		this.stmt.setObject(parameterIndex, x);
	}

	public void setObject(int parameterIndex, Object x, int targetSqlType) throws SQLException {
		this.stmt.setObject(parameterIndex, x);
	}

	public void setObject(int parameterIndex, Object x) throws SQLException {
		this.stmt.setObject(parameterIndex, x);
	}

	public boolean execute() throws SQLException {
		return this.stmt.execute();
	}

	public void addBatch() throws SQLException {
		this.stmt.addBatch();
	}

	public void setCharacterStream(int parameterIndex, Reader reader, int length) throws SQLException {
		this.stmt.setObject(parameterIndex, reader, length);
	}

	public void setRef(int i, Ref x) throws SQLException {
		this.stmt.setRef(i, x);
	}

	public void setBlob(int i, Blob x) throws SQLException {
		this.stmt.setBlob(i, x);
	}

	public void setClob(int i, Clob x) throws SQLException {
		this.stmt.setObject(i, x);
	}

	public void setArray(int i, Array x) throws SQLException {
		this.stmt.setObject(i, x);
	}

	public ResultSetMetaData getMetaData() throws SQLException {
		return this.stmt.getMetaData();
	}

	public void setDate(int parameterIndex, Date x, Calendar cal) throws SQLException {
		this.stmt.setDate(parameterIndex, x, cal);
	}

	public void setTime(int parameterIndex, Time x, Calendar cal) throws SQLException {
		this.stmt.setTime(parameterIndex, x, cal);
	}

	public void setTimestamp(int parameterIndex, Timestamp x, Calendar cal) throws SQLException {
		this.stmt.setTimestamp(parameterIndex, x, cal);
	}

	public void setNull(int paramIndex, int sqlType, String typeName) throws SQLException {
		this.stmt.setNull(paramIndex, sqlType, typeName);
	}

	public void setURL(int parameterIndex, URL x) throws SQLException {
		this.stmt.setURL(parameterIndex, x);
	}

	public ParameterMetaData getParameterMetaData() throws SQLException {
		return this.stmt.getParameterMetaData();
	}

	public ResultSet executeQuery(String sql) throws SQLException {
		return this.stmt.executeQuery(sql);
	}

	public int executeUpdate(String sql) throws SQLException {
		return this.stmt.executeUpdate(sql);
	}

	public void close() throws SQLException {
		Connection conn = null;
		try {
			conn = this.stmt.getConnection();
			this.stmt.close();
		} catch (Exception ex) {
		}
		try {
			if (conn != null)
				conn.close();
		} catch (Exception ex) {
			CoreLogger.getLogger().error(ex);
		}

	}

	public int getMaxFieldSize() throws SQLException {
		return this.stmt.getMaxFieldSize();
	}

	public void setMaxFieldSize(int max) throws SQLException {
		this.stmt.setMaxFieldSize(max);
	}

	public int getMaxRows() throws SQLException {
		return this.stmt.getMaxRows();
	}

	public void setMaxRows(int max) throws SQLException {
		this.stmt.setMaxRows(max);
	}

	public void setEscapeProcessing(boolean enable) throws SQLException {
		this.stmt.setEscapeProcessing(enable);
	}

	public int getQueryTimeout() throws SQLException {
		return this.stmt.getQueryTimeout();
	}

	public void setQueryTimeout(int seconds) throws SQLException {
		this.stmt.setQueryTimeout(seconds);
	}

	public void cancel() throws SQLException {
		this.stmt.cancel();
	}

	public SQLWarning getWarnings() throws SQLException {
		return this.stmt.getWarnings();
	}

	public void clearWarnings() throws SQLException {
		this.stmt.clearWarnings();
	}

	public void setCursorName(String name) throws SQLException {
		this.stmt.setCursorName(name);
	}

	public boolean execute(String sql) throws SQLException {
		return this.stmt.execute(sql);
	}

	public ResultSet getResultSet() throws SQLException {
		return this.stmt.getResultSet();
	}

	public int getUpdateCount() throws SQLException {
		return this.stmt.getUpdateCount();
	}

	public boolean getMoreResults() throws SQLException {
		return this.stmt.getMoreResults();
	}

	public void setFetchDirection(int direction) throws SQLException {
		this.stmt.setFetchDirection(direction);
	}

	public int getFetchDirection() throws SQLException {
		return stmt.getFetchDirection();
	}

	public void setFetchSize(int rows) throws SQLException {
		this.stmt.setFetchSize(rows);
	}

	public int getFetchSize() throws SQLException {
		return this.stmt.getFetchSize();
	}

	public int getResultSetConcurrency() throws SQLException {
		return this.stmt.getResultSetConcurrency();
	}

	public int getResultSetType() throws SQLException {
		return this.stmt.getResultSetType();
	}

	public void addBatch(String sql) throws SQLException {
		this.stmt.addBatch(sql);
	}

	public void clearBatch() throws SQLException {
		this.stmt.clearBatch();
	}

	public int[] executeBatch() throws SQLException {
		return this.stmt.executeBatch();
	}

	public Connection getConnection() throws SQLException {
		return this.stmt.getConnection();
	}

	public boolean getMoreResults(int current) throws SQLException {
		return this.stmt.getMoreResults(current);
	}

	public ResultSet getGeneratedKeys() throws SQLException {
		return this.stmt.getGeneratedKeys();
	}

	public int executeUpdate(String sql, int autoGeneratedKeys) throws SQLException {
		return this.stmt.executeUpdate(sql, autoGeneratedKeys);
	}

	public int executeUpdate(String sql, int[] columnIndexes) throws SQLException {
		return this.stmt.executeUpdate(sql, columnIndexes);
	}

	public int executeUpdate(String sql, String[] columnNames) throws SQLException {
		return this.stmt.executeUpdate(sql, columnNames);
	}

	public boolean execute(String sql, int autoGeneratedKeys) throws SQLException {
		return this.stmt.execute(sql, autoGeneratedKeys);
	}

	public boolean execute(String sql, int[] columnIndexes) throws SQLException {
		return this.stmt.execute(sql, columnIndexes);
	}

	public boolean execute(String sql, String[] columnNames) throws SQLException {
		return this.stmt.execute(sql, columnNames);
	}

	public int getResultSetHoldability() throws SQLException {
		return this.stmt.getResultSetHoldability();
	}

}
