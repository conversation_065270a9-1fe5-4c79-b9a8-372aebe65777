package org.easitline.common.db.log;



import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
  
/**
 * Easitline 操作日志类
 * <AUTHOR>
 *
 */
public class JDBCWarnLogger  {

	private static final   String APP_NAME = "easitline-jdbc";
	
	private static final   String LOG_NAME_DANGER = "easitline-jdbc-danger";
	
	
	public static Logger getDangerLogger(){
		return LogEngine.getLogger(APP_NAME,LOG_NAME_DANGER);
	}
}
