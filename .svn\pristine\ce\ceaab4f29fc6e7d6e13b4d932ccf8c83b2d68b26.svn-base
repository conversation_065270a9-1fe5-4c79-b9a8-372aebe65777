package org.easitline.common.db.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import org.easitline.common.db.EasyRow;

import com.alibaba.fastjson.JSONObject;


public class EasyRowImpl implements EasyRow,java.io.Serializable{

  private static final long serialVersionUID = 1L;

  /**
   * 保存当前行的信息 列名＝列值
  */
  private Properties pro;

  /**
   * 保存列的列名信息
   */

  private String[] column;

  protected EasyRowImpl(String[] column) {
    pro = new Properties();
    this.column = column;
  }

  /**
   * @param columnIndex 当前列得下标，下标从1开始
   * @param columnName  列名
   * @param value  列值
   */

  public void setColumnValue(int columnIndex, String value) {
    if(value == null) value  = "__column_is_null__";
    pro.setProperty(column[columnIndex - 1], value);
  }
 
  /**
   * 根据列名取得列的值
   * @param columnName 列名
   * @return  列值
   */
  public String getColumnValue(String columnName) {
	 return   this.getColumnValue(columnName, "");
  }
  
  
  /**
   * 根据列名取得列的值
   * @param columnName 列名
   * @return  列值
   */
  public String getColumnValue(String columnName ,String defaultValue) {
	    columnName = columnName.toUpperCase();
	    String value =  pro.getProperty(columnName);
	    if(value == null) return defaultValue;
	    if(value.equals("__column_is_null__")) return "";
	    return value;
  }

  /**
   * 根据列名取得列的值
   * @param columnName 列名
   * @return  列值
   */
  public String getColumnValue(int columnIndex) {
	  	return this.getColumnValue(columnIndex, "");
  }
  
  
  public String getColumnValue(int columnIndex,String defaultValue) {
	    String value = pro.getProperty(column[columnIndex - 1]);
	    if(value == null) return defaultValue;
	    if(value.equals("__column_is_null__")) return defaultValue;
	    return value;

 }

  /**
   * 取得列名
   * @param columnIndex int  列下标
   * @return String 列名
   */
  public String getColumnName(int columnIndex){
	    String value =  column[columnIndex - 1];
	    if(value == null) return value;
	    if(value.equals("__column_is_null__")) return null;
	    return value;
  }

  /**
     * 取得列名
     * @param columnIndex int  列下标
     * @return String 列名
     */
    public String[] getColumnNames(){
    	return this.column;
    }

  /**
   * 取得列数
   * @return int  列数量
   */
  public int getColumnCount(){
	  return column.length;
  }
	@Override
	public JSONObject toJSONObject() {
		int columnCount = this.getColumnCount();
		JSONObject json = new JSONObject(true);
		for(int i=1; i<=columnCount; i++) {
			json.put(this.getColumnName(i), this.getColumnValue(i));
		}
		return json;
	}
	
	
	@Override
	public Map<String,String> toMap() {
		int columnCount = this.getColumnCount();
		 Map<String,String> map = new HashMap<String,String>();
		for(int i=1; i<=columnCount; i++) {
			map.put(this.getColumnName(i), this.getColumnValue(i));
		}
		return map;
	}

}
