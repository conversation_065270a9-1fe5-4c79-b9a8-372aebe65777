package org.easitline.common.db;


import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.easitline.common.utils.string.StringUtils;

/**
 * 生成SQL类
 *
 */
public class EasySQL {

	private StringBuffer sqlBuilder = new StringBuffer();
	
	private List<Object> parameters = new ArrayList<>();
	
	private EasyQuery query=null;
	
	public EasySQL(){
	}
	public EasySQL(String sql){
		sqlBuilder.append(sql);
	}
	
	public EasySQL(EasyQuery query,String sql){
		this.query=query;
		sqlBuilder.append(sql);
	}
	public void setRecord(EasyRecord record) {
		record.setSQL(sqlBuilder, parameters);
	}
	public void execute(){
		if(query!=null){
			try {
				query.execute(this.getSQL(), this.getParams());
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}else{
			System.out.println("query is not null!");
		}
	}
	
	/**
	 * append一个sql
	 * @param paramValue  参数值，如果这个值为空，则不append
	 * @param sql  sql，可带?的SQL，程序自动会根据?的个数来生成参数个数
	 * 默认跳过参数为空的
	 */
	public EasySQL append(Object paramValue,String sql){
		return append(paramValue,sql,true);
	}
	/**
	 * append一个sql
	 * @param paramValue  参数值
	 * @param sql  sql，可带?的SQL，程序自动会根据?的个数来生成参数个数
	 * @param skipEmpty 是否跳过参数为空的sql
	 */
	public EasySQL append(Object paramValue,String sql,boolean skipEmpty){
		if(skipEmpty){
			if(paramValue!=null&&StringUtils.notBlank(StringUtils.trimToEmpty(paramValue.toString()))){
				int count = countParam(sql,"?");
				for(int i = 0 ;i <count ;i++){
					parameters.add(paramValue);
				}
				sqlBuilder.append(" ").append(sql);
			}
		}else{
			parameters.add(paramValue);
			sqlBuilder.append(" ").append(sql);
		}
		return this;
	}
	/**
	 * 
	 * @param paramValue 参数
	 * @param sql SQL语句
	 * @param defaultVal参数为空的时候取默认值
	 * @return
	 */
	public EasySQL append(Object paramValue,String sql,String defaultVal){
		if(paramValue == null||StringUtils.isBlank(StringUtils.trimToEmpty(paramValue.toString()))){
			paramValue = defaultVal;
		}
		return append(paramValue,sql,false);
	}
	/**
	 * 添加排序
	 * @param sortName 需要排序的字段
	 * @param sortType 排序类型 ASC DESC
	 * @return
	 */
	public EasySQL appendSort(String sortName,String sortType){
		return appendSort(sortName,sortType,null);
	}
	public EasySQL appendSort(String sortName,String sortType,String defaultSort){
		if(StringUtils.isBlank(defaultSort)&&StringUtils.notBlank(sortName)){
			sqlBuilder.append(" ORDER BY ").append(sortName).append(" ").append(sortType);
		}else if(StringUtils.notBlank(defaultSort)&&StringUtils.notBlank(sortName)){
			sqlBuilder.append(" ORDER BY ").append(sortName).append(" ").append(sortType).append(",").append(defaultSort);
		}else if(StringUtils.notBlank(defaultSort)&&StringUtils.isBlank(sortName)){
			sqlBuilder.append(" ORDER BY ").append(defaultSort);
		}
		return this;
	}
	
	/**
	 * append sql 不带参数,可用于在sql最后加入order by 等参数
	 * @param sql
	 * @return
	 */
	public EasySQL  append(String sql){
		if(sql!=null) {
			sqlBuilder.append(" ").append(sql);
		}
		return this;
	}
	
	private EasySQL appendInInternal(Object[] values, String sql) {
		if(values != null){
			int count = values.length;
			if(count==0)return this;
			if(count==1){
				sqlBuilder.append(" ").append(sql).append(" = ?");
				parameters.add(values[0]);
				return this;
			}
			StringBuffer buffer=new StringBuffer();
			buffer.append(" ").append(sql).append(" in (");
			for(int i = 0 ;i <count ;i++){
				buffer.append("?,");
			}
			buffer.delete(buffer.length()-1, buffer.length());
			buffer.append(")");
			for(int i = 0 ;i <count ;i++){
				parameters.add(values[i]);
			}
			sqlBuilder.append(" ").append(buffer);
	    }
		return this;
	}

	public EasySQL appendIn(int[] paramValues,String sql){
		if(paramValues != null){
			Integer[] values = new Integer[paramValues.length];
			for(int i = 0; i < paramValues.length; i++) {
				values[i] = paramValues[i];
			}
			return appendInInternal(values, sql);
		}
		return this;
	}

	public EasySQL appendIn(String[] paramValues,String sql){
		return appendInInternal(paramValues, sql);
	}
	
	/**
	 * append 左like
	 * @param paramValue
	 * @param sql
	 * @return
	 */
	public EasySQL  appendLLike(Object paramValue,String sql){
		if(paramValue != null){
			if(StringUtils.notBlank(paramValue.toString())){
				int count = countParam(sql,"?");
				for(int i = 0 ;i <count ;i++){
					parameters.add("%"+paramValue);
				}
				sqlBuilder.append(" ").append(sql);
			}
		}
		return this;
	}
	
	/**
	 * append  右like
	 * @param paramValue
	 * @param sql
	 * @return
	 */
	public EasySQL  appendRLike(Object paramValue,String sql){
		if(paramValue != null){
			if(StringUtils.notBlank(paramValue.toString())){
				int count = countParam(sql,"?");
				for(int i = 0 ;i <count ;i++){
					parameters.add(paramValue+"%");
				}
				sqlBuilder.append(" ").append(sql);
			}
		}
		return this;
	}
	
	
	/**
	 * append like
	 * @param paramValue
	 * @param sql
	 * @return
	 */
	public EasySQL  appendLike(Object paramValue,String sql){
		if(paramValue != null){
			if(StringUtils.notBlank(paramValue.toString())){
				int count = countParam(sql,"?");
				for(int i = 0 ;i <count ;i++){
					parameters.add("%"+paramValue+"%");
				}
				sqlBuilder.append(" ").append(sql);
			}
		}
		return this;
	}
	/**
	 * 计算有多个少参数
	 * @param str
	 * @param substr
	 * @return
	 */
	private int countParam(String str, String substr) {
		int index = 0;
		int count = 0;
		int fromindex = 0;
		while ((index = str.indexOf(substr, fromindex)) != -1) {
			fromindex = index + substr.length();
			count++;
		}
		return count;
	}
	
	/**
	 * 获得执行的SQL
	 * @return
	 */
	public String getSQL(){
		return sqlBuilder.toString();
	}
	
	/**
	 * 获得执行的参数
	 * @return
	 */
	public Object[] getParams(){
		return parameters.toArray();
	}
	
	public StringBuffer getSqlBuf() {
		return sqlBuilder;
	}
	public List<Object> getParamsList() {
		return parameters;
	}
	
	public void addParams(Object param){
		parameters.add(param);
	}
	
	public String toFullSql() {
		String sql = getSQL();
		Object[] params = getParams();
		return SqlHelper.showFullSql(sql, params);
	}
	
	@Deprecated
	public String getFullSq() {
		return toFullSql();
	}
	
	public String getFullSql() {
		return toFullSql();
	}
	
	public static EasySQL builder() {
		return new EasySQL();
	}
	
	public static EasySQL builder(String sql) {
		return new EasySQL(sql);
	}
}
