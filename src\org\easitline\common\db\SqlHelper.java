package org.easitline.common.db;

import java.sql.SQLException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.log.JDBCErrorLogger;
import org.easitline.common.db.log.JDBCLogger;
import org.easitline.common.db.log.JDBCWarnLogger;

import com.alibaba.fastjson.JSONObject;

public class SqlHelper {
	
	private static volatile ExecutorService executor;
	
	private static final Object LOCK = new Object();

	private static final int QUEUE_CAPACITY = 5000;

	private static void checkExecutorState() {
		synchronized (LOCK) {
			boolean shouldRun = sendMsgFlag();
			
			// 需要关闭
			if (!shouldRun && executor != null && !executor.isShutdown()) {
				shutdown();
				executor = null;
				return;
			}
			
			// 需要开启
			if (shouldRun && (executor == null || executor.isShutdown())) {
				executor = new ThreadPoolExecutor(
					2, 4,
					60L, TimeUnit.SECONDS,
					new LinkedBlockingQueue<>(QUEUE_CAPACITY),
					new ThreadFactory() {
						private final AtomicInteger counter = new AtomicInteger(1);
						@Override
						public Thread newThread(Runnable r) {
							Thread thread = new Thread(r, "SQL-Monitor-Thread-" + counter.getAndIncrement());
							thread.setDaemon(true);
							return thread;
						}
					},
					new ThreadPoolExecutor.CallerRunsPolicy()
				);
			}
		}
	}


	public static void addNotify(long exeTime,String sql,String param,ConnectionMetaData  metaData,String stackTrace) {
	    checkExecutorState();
	    if (executor == null || executor.isShutdown()) {
	        return;
	    }
	    
	    executor.execute(new Runnable() {
	        @Override
	        public void run() {
	            try {
	                JSONObject notifyObj = new JSONObject();
	                notifyObj.put("exeTime", exeTime);
	                notifyObj.put("sql", sql);
	                notifyObj.put("param", param);
	                notifyObj.put("stackTrace", stackTrace);
	                if(metaData!=null) {
	                	notifyObj.put("appName", metaData.getAppName());
	                	notifyObj.put("dsName", metaData.getSysDatasourceName());
	                }
	                // 调用通知服务
	                JDBCLogger.getLogger().info("启动SQL执行超过2S告警通知...");
	                IService service = ServiceContext.getService("SQL_WARN_NOTIFY_SERVICE");
	                if (service != null) {
	                    service.invoke(notifyObj);
	                }
	            } catch (Exception ex) {
	                CoreLogger.getPlatform().error("处理通知消息异常", ex);
	            }
	        }
	    });
	}
	
	public static String showFullSql(String sql, Object[] params) {
		if (sql == null || sql.isEmpty() || params == null || params.length == 0) {
			return sql;
		}
		
		// 预估容量为SQL长度 + 参数数量 * 20
		StringBuilder result = new StringBuilder(sql.length() + params.length * 20);
		int paramIndex = 0;
		
		try {
			char[] sqlChars = sql.toCharArray(); // 避免重复charAt调用
			for (char c : sqlChars) {
				if (c == '?' && paramIndex < params.length) {
					appendParam(result, params[paramIndex++]);
				} else {
					result.append(c);
				}
			}
			return result.toString();
		} catch (Exception e) {
			return String.format("Original SQL: %s, Params: %s", sql, paramToString(params));
		}
	}
	
	private static void appendParam(StringBuilder sb, Object param) {
		if (param == null) {
			sb.append("NULL");
			return;
		}
		
		if (param instanceof String || param instanceof java.util.Date) {
			sb.append('\'').append(param).append('\'');
		} else {
			sb.append(param);
		}
	}
	
	public static String paramToString(Object[] params){
		if(params == null) return "{}";
		StringBuilder buf = new StringBuilder(params.length * 16);
		buf.append("{");
		for (int i = 0; i < params.length; i++) {
			if(i == params.length -1) 
			   buf.append(params[i]);
			else 
				buf.append(params[i]).append(",");
		}
		buf.append("}");
		return buf.toString();
	}
	
	public static void errorLogOut(Logger appLogger,SQLException ex,String sql,Object[] params,ConnectionMetaData  metaData){
		StringBuilder msg = new StringBuilder();
		msg.append("Error! SQL exception cause:"+ex.getMessage()+",sql:"+sql+",params["+paramToString(params)+"]");
		if (metaData!=null) {
			msg.append(",appName["+metaData.getAppName()+"]");
			msg.append(",dsName["+metaData.getSysDatasourceName()+"]");
		}
		String fullSql = showFullSql(sql, params);
		JDBCErrorLogger.getLogger().error("SQLException："+ex.getMessage()+"["+fullSql+"]");
		JDBCErrorLogger.getLogger().error(msg.toString(),ex);
		if(appLogger!=null){
			appLogger.error(msg.toString(),ex);
		}
		if(metaData!=null) {
			sendSqlErrorLogMsg(fullSql, ex.getMessage(), metaData.getAppName(), metaData.getSysDatasourceName());
		}else {
			sendSqlErrorLogMsg(fullSql, ex.getMessage(),"null","null");
		}
	}
	
	public static StackTraceElement getStackTrace(StackTraceElement[] stackTrace){
		if (stackTrace == null || stackTrace.length == 0) {
			return null;
		}
		for (StackTraceElement element : stackTrace) {
			String className = element.getClassName();
			// 跳过框架相关的包
			if (!className.startsWith("org.easitline") && 
				!className.startsWith("java.") && 
				!className.startsWith("sun.") &&
				!className.startsWith("javax.")) {
				return element;
			}
		}
		return null;
	}
	
	public static void timeoutLogOut(Logger appLogger, long exetime, String sql, Object[] params,ConnectionMetaData  metaData) {
		String parmaString = paramToString(params);		
		String trace = "";
		StackTraceElement  element = getStackTrace(Thread.currentThread().getStackTrace());
		if(element!=null) {
			trace = element.toString();
		}
		StringBuilder msg = new StringBuilder();
		msg.append("["+exetime+"ms] "+sql+",params["+parmaString+"]");
		if(metaData!=null){
			msg.append(",appName["+metaData.getAppName()+"]");
			msg.append(",dsName["+metaData.getSysDatasourceName()+"]");
		}
		msg.append(" - "+trace);
		if (exetime > 2000) {
			JDBCWarnLogger.getDangerLogger().warn(msg.toString());
			if (appLogger != null) { 
				appLogger.warn(msg.toString());
			}
			addNotify(exetime,sql,parmaString,metaData,trace);
			if(exetime>10000) {
				if(metaData!=null) {
					sendSqlSlowLogMsg(showFullSql(sql, params), exetime, metaData.getAppName(),metaData.getSysDatasourceName());
				}else {
					sendSqlSlowLogMsg(showFullSql(sql, params), exetime,"null","null");
				}
		    }
		}else{
			if (appLogger != null) {
			   appLogger.info(msg.toString());
			}

		}
		JDBCLogger.getLogger().info(msg.toString());
	}
	
	
	public static void listSizeLogOut(Logger appLogger,long recordCount,String sql,Object[] params){
		String parmaString = paramToString(params);
		String msg = "[list.size()->"+recordCount+"] "+sql+",params["+parmaString+"] - "+getStackTrace(Thread.currentThread().getStackTrace())+" ";
		JDBCLogger.getLogger().warn(msg);
		if(appLogger != null) appLogger.warn(msg);
	}
	
	private static boolean sendMsgFlag() {
		return "1".equals(ServerContext.getProperties("sqlAlarmNoticeFlag", "0"));
	}
	
	private static void sendSqlSlowLogMsg(String sql, long time, String appName, String dsName) {
		checkExecutorState();
		if (executor == null || executor.isShutdown()) {
			return;
		}
		
		executor.execute(new Runnable() {
			@Override
			public void run() {
				try {
					JSONObject params = new JSONObject();
					params.put("type", "slow");
					params.put("sql", sql);
					params.put("time", time);
					params.put("appName", appName);
					params.put("dsName", dsName);
					
					LogEngine.getLogger("easitline-sql-slow").error(params.toJSONString());
					
					IService service = ServiceContext.getService("SQL-MONITOR-SERVICE");
					if (service != null) {
						service.invoke(params);
					}
				} catch (Exception ex) {
					CoreLogger.getPlatform().error("处理慢SQL监控消息异常", ex);
				}
			}
		});
	}
	
	private static void sendSqlErrorLogMsg(String sql, String errorMsg, String appName, String dsName) {
		checkExecutorState();
		if (executor == null || executor.isShutdown()) {
			return;
		}
		
		executor.execute(new Runnable() {
			@Override
			public void run() {
				try {
					JSONObject params = new JSONObject();
					params.put("type", "error");
					params.put("sql", sql);
					params.put("errorMsg", errorMsg);
					params.put("appName", appName);
					params.put("dsName", dsName);
					
					LogEngine.getLogger("easitline-sql-error").error(params.toJSONString());
					
					IService service = ServiceContext.getService("SQL-MONITOR-SERVICE");
					if (service != null) {
						service.invoke(params);
					}
				} catch (Exception ex) {
					CoreLogger.getPlatform().error("处理SQL错误监控消息异常", ex);
				}
			}
		});
	}

	private static void shutdown() {
		if (executor != null && !executor.isShutdown()) {
			executor.shutdown();
			try {
				if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
					executor.shutdownNow();
				}
			} catch (InterruptedException e) {
				executor.shutdownNow();
				Thread.currentThread().interrupt();
			}finally {
				executor = null;
			}
		}
		
	}

}

