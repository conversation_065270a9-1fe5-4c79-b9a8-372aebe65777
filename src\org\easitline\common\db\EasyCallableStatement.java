package org.easitline.common.db;

import java.io.InputStream;
import java.io.Reader;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.Array;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.Date;
import java.sql.NClob;
import java.sql.ParameterMetaData;
import java.sql.Ref;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.RowId;
import java.sql.SQLException;
import java.sql.SQLWarning;
import java.sql.SQLXML;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.Map;

public class EasyCallableStatement implements java.sql.CallableStatement {

  private java.sql.CallableStatement stmt;

  public EasyCallableStatement(java.sql.CallableStatement stmt) {
    this.stmt = stmt;
  }

  public void registerOutParameter(int parameterIndex, int sqlType) throws
      SQLException {
    stmt.registerOutParameter(parameterIndex, sqlType);
  }

  public void registerOutParameter(int parameterIndex, int sqlType, int scale) throws
      SQLException {
    stmt.registerOutParameter(parameterIndex, sqlType, scale);
  }

  public boolean wasNull() throws SQLException {
    return stmt.wasNull();
  }

  public String getString(int parameterIndex) throws SQLException {
    return stmt.getString(parameterIndex);
  }

  public boolean getBoolean(int parameterIndex) throws SQLException {
    return stmt.getBoolean(parameterIndex);
  }

  public byte getByte(int parameterIndex) throws SQLException {
    return stmt.getByte(parameterIndex);
  }

  public short getShort(int parameterIndex) throws SQLException {
    return stmt.getShort(parameterIndex);
  }

  public int getInt(int parameterIndex) throws SQLException {
    return stmt.getInt(parameterIndex);
  }

  public long getLong(int parameterIndex) throws SQLException {
    return stmt.getLong(parameterIndex);
  }

  public float getFloat(int parameterIndex) throws SQLException {
    return stmt.getFloat(parameterIndex);
  }

  public double getDouble(int parameterIndex) throws SQLException {
    return stmt.getFloat(parameterIndex);

  }

  public BigDecimal getBigDecimal(int parameterIndex, int scale) throws
      SQLException {
    return stmt.getBigDecimal(parameterIndex);
  }

  public byte[] getBytes(int parameterIndex) throws SQLException {
    return stmt.getBytes(parameterIndex);
  }

  public Date getDate(int parameterIndex) throws SQLException {
    return stmt.getDate(parameterIndex);
  }

  public Time getTime(int parameterIndex) throws SQLException {
    return stmt.getTime(parameterIndex);
  }

  public Timestamp getTimestamp(int parameterIndex) throws SQLException {
    return stmt.getTimestamp(parameterIndex);
  }

  public Object getObject(int parameterIndex) throws SQLException {
    return stmt.getObject(parameterIndex);
  }

  public BigDecimal getBigDecimal(int parameterIndex) throws SQLException {
    return stmt.getBigDecimal(parameterIndex);
  }

  public Object getObject(int i, Map map) throws SQLException {
    return stmt.getObject(i, map);
  }

  public Ref getRef(int i) throws SQLException {
    return stmt.getRef(i);
  }

  public Blob getBlob(int i) throws SQLException {
    return stmt.getBlob(i);
  }

  public Clob getClob(int i) throws SQLException {
    return stmt.getClob(i);
  }

  public Array getArray(int i) throws SQLException {
    return stmt.getArray(i);
  }

  public Date getDate(int parameterIndex, Calendar cal) throws SQLException {
    return stmt.getDate(parameterIndex, cal);
  }

  public Time getTime(int parameterIndex, Calendar cal) throws SQLException {
    return stmt.getTime(parameterIndex, cal);
  }

  public Timestamp getTimestamp(int parameterIndex, Calendar cal) throws  SQLException {
    return stmt.getTimestamp(parameterIndex, cal);
  }

  public void registerOutParameter(int paramIndex, int sqlType, String typeName) throws SQLException {
    stmt.registerOutParameter(paramIndex, sqlType, typeName);
  }

  public void registerOutParameter(String parameterName, int sqlType) throws SQLException {
    stmt.registerOutParameter(parameterName, sqlType);
  }

  public void registerOutParameter(String parameterName, int sqlType, int scale) throws SQLException {
    stmt.registerOutParameter(parameterName, sqlType, scale);
  }

  public void registerOutParameter(String parameterName, int sqlType,String typeName) throws SQLException {
    stmt.registerOutParameter(parameterName, sqlType, typeName);
  }

  public URL getURL(int parameterIndex) throws SQLException {
    return stmt.getURL(parameterIndex);
  }

  public void setURL(String parameterName, URL val) throws SQLException {
    stmt.setURL(parameterName, val);
  }

  public void setNull(String parameterName, int sqlType) throws SQLException {
    stmt.setNull(parameterName, sqlType);
  }

  public void setBoolean(String parameterName, boolean x) throws SQLException {
    stmt.setBoolean(parameterName, x);
  }

  public void setByte(String parameterName, byte x) throws SQLException {
    stmt.setByte(parameterName, x);
  }

  public void setShort(String parameterName, short x) throws SQLException {
    stmt.setShort(parameterName, x);
  }

  public void setInt(String parameterName, int x) throws SQLException {
    stmt.setInt(parameterName, x);
  }

  public void setLong(String parameterName, long x) throws SQLException {
    stmt.setLong(parameterName, x);
  }

  public void setFloat(String parameterName, float x) throws SQLException {
    stmt.setFloat(parameterName, x);
  }

  public void setDouble(String parameterName, double x) throws SQLException {
    stmt.setDouble(parameterName, x);
  }

  public void setBigDecimal(String parameterName, BigDecimal x) throws
      SQLException {
    stmt.setBigDecimal(parameterName, x);
  }

  public void setString(String parameterName, String x) throws SQLException {
    stmt.setString(parameterName, x);
  }

  public void setBytes(String parameterName, byte[] x) throws SQLException {
    stmt.setBytes(parameterName, x);
  }

  public void setDate(String parameterName, Date x) throws SQLException {
    stmt.setDate(parameterName, x);
  }

  public void setTime(String parameterName, Time x) throws SQLException {
    stmt.setTime(parameterName, x);
  }

  public void setTimestamp(String parameterName, Timestamp x) throws
      SQLException {
    stmt.setTimestamp(parameterName, x);
  }

  public void setAsciiStream(String parameterName, InputStream x, int length) throws
      SQLException {
    stmt.setAsciiStream(parameterName, x, length);
  }

  public void setBinaryStream(String parameterName, InputStream x, int length) throws
      SQLException {
    stmt.setBinaryStream(parameterName, x, length);
  }

  public void setObject(String parameterName, Object x, int targetSqlType, int scale) throws SQLException {
    stmt.setObject(parameterName, x, scale);
  }

  public void setObject(String parameterName, Object x, int targetSqlType) throws
      SQLException {
    stmt.setObject(parameterName, x, targetSqlType);
  }

  public void setObject(String parameterName, Object x) throws SQLException {
    stmt.setObject(parameterName, x);
  }

  public void setCharacterStream(String parameterName, Reader reader,
                                 int length) throws SQLException {
    stmt.setCharacterStream(parameterName, reader, length);
  }

  public void setDate(String parameterName, Date x, Calendar cal) throws
      SQLException {
    stmt.setDate(parameterName, x, cal);
  }

  public void setTime(String parameterName, Time x, Calendar cal) throws
      SQLException {
    stmt.setTime(parameterName, x, cal);
  }

  public void setTimestamp(String parameterName, Timestamp x, Calendar cal) throws
      SQLException {
    stmt.setTimestamp(parameterName, x, cal);
  }

  public void setNull(String parameterName, int sqlType, String typeName) throws  SQLException {
    stmt.setNull(parameterName, sqlType, typeName);
  }

  public String getString(String parameterName) throws SQLException {
    return stmt.getString(parameterName);
  }

  public boolean getBoolean(String parameterName) throws SQLException {
    return stmt.getBoolean(parameterName);
  }

  public byte getByte(String parameterName) throws SQLException {
    return stmt.getByte(parameterName);
  }

  public short getShort(String parameterName) throws SQLException {
    return stmt.getShort(parameterName);
  }

  public int getInt(String parameterName) throws SQLException {
    return stmt.getInt(parameterName);
  }

  public long getLong(String parameterName) throws SQLException {
    return stmt.getLong(parameterName);
  }

  public float getFloat(String parameterName) throws SQLException {
    return stmt.getFloat(parameterName);
  }

  public double getDouble(String parameterName) throws SQLException {
    return stmt.getDouble(parameterName);
  }

  public byte[] getBytes(String parameterName) throws SQLException {
    return stmt.getBytes(parameterName);
  }

  public Date getDate(String parameterName) throws SQLException {
    return stmt.getDate(parameterName);
  }

  public Time getTime(String parameterName) throws SQLException {
    return stmt.getTime(parameterName);
  }

  public Timestamp getTimestamp(String parameterName) throws SQLException {
    return stmt.getTimestamp(parameterName);
  }

  public Object getObject(String parameterName) throws SQLException {
    return stmt.getObject(parameterName);
  }

  public BigDecimal getBigDecimal(String parameterName) throws SQLException {
    return stmt.getBigDecimal(parameterName);
  }

  public Object getObject(String parameterName, Map map) throws SQLException {
    return stmt.getObject(parameterName);
  }

  public Ref getRef(String parameterName) throws SQLException {
    return stmt.getRef(parameterName);
  }

  public Blob getBlob(String parameterName) throws SQLException {
    return stmt.getBlob(parameterName);
  }

  public Clob getClob(String parameterName) throws SQLException {
    return stmt.getClob(parameterName);
  }

  public Array getArray(String parameterName) throws SQLException {
    return stmt.getArray(parameterName);
  }

  public Date getDate(String parameterName, Calendar cal) throws SQLException {
    return stmt.getDate(parameterName, cal);
  }

  public Time getTime(String parameterName, Calendar cal) throws SQLException {
    return stmt.getTime(parameterName, cal);
  }

  public Timestamp getTimestamp(String parameterName, Calendar cal) throws
      SQLException {
    return stmt.getTimestamp(parameterName, cal);
  }

  public URL getURL(String parameterName) throws SQLException {
    return stmt.getURL(parameterName);
  }

  public ResultSet executeQuery() throws SQLException {
    return stmt.executeQuery();
  }

  public int executeUpdate() throws SQLException {
    return stmt.executeUpdate();
  }

  public void setNull(int parameterIndex, int sqlType) throws SQLException {
    stmt.setNull(parameterIndex, sqlType);
  }

  public void setBoolean(int parameterIndex, boolean x) throws SQLException {
    this.stmt.setBoolean(parameterIndex, x);
  }

  public void setByte(int parameterIndex, byte x) throws SQLException {
    this.stmt.setByte(parameterIndex, x);
  }

  public void setShort(int parameterIndex, short x) throws SQLException {
    this.stmt.setShort(parameterIndex, x);
  }

  public void setInt(int parameterIndex, int x) throws SQLException {
    this.stmt.setInt(parameterIndex, x);
  }

  public void setLong(int parameterIndex, long x) throws SQLException {
    this.stmt.setLong(parameterIndex, x);
  }

  public void setFloat(int parameterIndex, float x) throws SQLException {
    this.stmt.setFloat(parameterIndex, x);
  }

  public void setDouble(int parameterIndex, double x) throws SQLException {
    this.stmt.setDouble(parameterIndex, x);
  }

  public void setBigDecimal(int parameterIndex, BigDecimal x) throws
      SQLException {
    this.stmt.setBigDecimal(parameterIndex, x);
  }

  public void setString(int parameterIndex, String x) throws SQLException {
    this.stmt.setString(parameterIndex, x);
  }

  public void setBytes(int parameterIndex, byte[] x) throws SQLException {
    this.stmt.setBytes(parameterIndex, x);
  }

  public void setDate(int parameterIndex, Date x) throws SQLException {
    this.stmt.setDate(parameterIndex, x);
  }

  public void setTime(int parameterIndex, Time x) throws SQLException {
    this.stmt.setTime(parameterIndex, x);
  }

  public void setTimestamp(int parameterIndex, Timestamp x) throws SQLException {
    this.stmt.setTimestamp(parameterIndex, x);
  }

  public void setAsciiStream(int parameterIndex, InputStream x, int length) throws
      SQLException {
    this.stmt.setAsciiStream(parameterIndex, x, length);
  }

  public void setUnicodeStream(int parameterIndex, InputStream x, int length) throws
      SQLException {
    this.stmt.setUnicodeStream(parameterIndex, x, length);
  }

  public void setBinaryStream(int parameterIndex, InputStream x, int length) throws
      SQLException {
    this.stmt.setBinaryStream(parameterIndex, x, length);
  }

  public void clearParameters() throws SQLException {
    this.stmt.clearParameters();
  }

  public void setObject(int parameterIndex, Object x, int targetSqlType,
                        int scale) throws SQLException {
    this.stmt.setObject(parameterIndex, x);
  }

  public void setObject(int parameterIndex, Object x, int targetSqlType) throws
      SQLException {
    this.stmt.setObject(parameterIndex, x);
  }

  public void setObject(int parameterIndex, Object x) throws SQLException {
    this.stmt.setObject(parameterIndex, x);
  }

  public boolean execute() throws SQLException {
    return this.stmt.execute();
  }

  public void addBatch() throws SQLException {
    this.stmt.addBatch();
  }

  public void setCharacterStream(int parameterIndex, Reader reader, int length) throws
      SQLException {
    this.stmt.setObject(parameterIndex, reader, length);
  }

  public void setRef(int i, Ref x) throws SQLException {
    this.stmt.setRef(i, x);
  }

  public void setBlob(int i, Blob x) throws SQLException {
    this.stmt.setBlob(i, x);
  }

  public void setClob(int i, Clob x) throws SQLException {
    this.stmt.setObject(i, x);
  }

  public void setArray(int i, Array x) throws SQLException {
    this.stmt.setObject(i, x);
  }

  public ResultSetMetaData getMetaData() throws SQLException {
    return this.stmt.getMetaData();
  }

  public void setDate(int parameterIndex, Date x, Calendar cal) throws
      SQLException {
    this.stmt.setDate(parameterIndex, x, cal);
  }

  public void setTime(int parameterIndex, Time x, Calendar cal) throws
      SQLException {
    this.stmt.setTime(parameterIndex, x, cal);
  }

  public void setTimestamp(int parameterIndex, Timestamp x, Calendar cal) throws
      SQLException {
    this.stmt.setTimestamp(parameterIndex, x, cal);
  }

  public void setNull(int paramIndex, int sqlType, String typeName) throws
      SQLException {
    this.stmt.setNull(paramIndex, sqlType, typeName);
  }

  public void setURL(int parameterIndex, URL x) throws SQLException {
    this.stmt.setURL(parameterIndex, x);
  }

  public ParameterMetaData getParameterMetaData() throws SQLException {
    return this.stmt.getParameterMetaData();
  }

  public ResultSet executeQuery(String sql) throws SQLException {
    return this.stmt.executeQuery(sql);
  }

  public int executeUpdate(String sql) throws SQLException {
    return this.stmt.executeUpdate(sql);
  }

  public void close() throws SQLException {
    Connection conn = null;
    try {
      conn = this.stmt.getConnection();
      this.stmt.close();
    }
    catch (Exception ex) {
    }
    try {
        if(conn != null) conn.close();
    }
      catch (Exception ex) {

    }

  }

  public int getMaxFieldSize() throws SQLException {
    return this.stmt.getMaxFieldSize();
  }

  public void setMaxFieldSize(int max) throws SQLException {
    this.stmt.setMaxFieldSize(max);
  }

  public int getMaxRows() throws SQLException {
    return this.stmt.getMaxRows();
  }

  public void setMaxRows(int max) throws SQLException {
    this.stmt.setMaxRows(max);
  }

  public void setEscapeProcessing(boolean enable) throws SQLException {
    this.stmt.setEscapeProcessing(enable);
  }

  public int getQueryTimeout() throws SQLException {
    return this.stmt.getQueryTimeout();
  }

  public void setQueryTimeout(int seconds) throws SQLException {
    this.stmt.setQueryTimeout(seconds);
  }

  public void cancel() throws SQLException {
    this.stmt.cancel();
  }

  public SQLWarning getWarnings() throws SQLException {
    return this.stmt.getWarnings();
  }

  public void clearWarnings() throws SQLException {
    this.stmt.clearWarnings();
  }

  public void setCursorName(String name) throws SQLException {
    this.stmt.setCursorName(name);
  }

  public boolean execute(String sql) throws SQLException {
    return this.stmt.execute(sql);
  }

  public ResultSet getResultSet() throws SQLException {
    return this.stmt.getResultSet();
  }

  public int getUpdateCount() throws SQLException {
    return this.stmt.getUpdateCount();
  }

  public boolean getMoreResults() throws SQLException {
    return this.stmt.getMoreResults();
  }

  public void setFetchDirection(int direction) throws SQLException {
    this.stmt.setFetchDirection(direction);
  }

  public int getFetchDirection() throws SQLException {
    return stmt.getFetchDirection();
  }

  public void setFetchSize(int rows) throws SQLException {
    this.stmt.setFetchSize(rows);
  }

  public int getFetchSize() throws SQLException {
    return this.stmt.getFetchSize();
  }

  public int getResultSetConcurrency() throws SQLException {
    return this.stmt.getResultSetConcurrency();
  }

  public int getResultSetType() throws SQLException {
    return this.stmt.getResultSetType();
  }

  public void addBatch(String sql) throws SQLException {
    this.stmt.addBatch(sql);
  }

  public void clearBatch() throws SQLException {
    this.stmt.clearBatch();
  }

  public int[] executeBatch() throws SQLException {
    return this.stmt.executeBatch();
  }

  public Connection getConnection() throws SQLException {
    return this.stmt.getConnection();
  }

  public boolean getMoreResults(int current) throws SQLException {
    return this.stmt.getMoreResults(current);
  }

  public ResultSet getGeneratedKeys() throws SQLException {
    return this.stmt.getGeneratedKeys();
  }

  public int executeUpdate(String sql, int autoGeneratedKeys) throws
      SQLException {
    return this.stmt.executeUpdate(sql, autoGeneratedKeys);
  }

  public int executeUpdate(String sql, int[] columnIndexes) throws SQLException {
    return this.stmt.executeUpdate(sql, columnIndexes);
  }

  public int executeUpdate(String sql, String[] columnNames) throws
      SQLException {
    return this.stmt.executeUpdate(sql, columnNames);
  }

  public boolean execute(String sql, int autoGeneratedKeys) throws SQLException {
    return this.stmt.execute(sql, autoGeneratedKeys);
  }

  public boolean execute(String sql, int[] columnIndexes) throws SQLException {
    return this.stmt.execute(sql, columnIndexes);
  }

  public boolean execute(String sql, String[] columnNames) throws SQLException {
    return this.stmt.execute(sql, columnNames);
  }

  public int getResultSetHoldability() throws SQLException {
    return this.stmt.getResultSetHoldability();
  }

	@Override
	public void setAsciiStream(int arg0, InputStream arg1) throws SQLException {
		
		
	}
	
	@Override
	public void setAsciiStream(int arg0, InputStream arg1, long arg2) throws SQLException {
		
		
	}
	
	@Override
	public void setBinaryStream(int arg0, InputStream arg1) throws SQLException {
		
		
	}
	
	@Override
	public void setBinaryStream(int arg0, InputStream arg1, long arg2) throws SQLException {
		
		
	}
	
	@Override
	public void setBlob(int arg0, InputStream arg1) throws SQLException {
		
		
	}
	
	@Override
	public void setBlob(int arg0, InputStream arg1, long arg2) throws SQLException {
		
		
	}
	
	@Override
	public void setCharacterStream(int arg0, Reader arg1) throws SQLException {
		
		
	}
	
	@Override
	public void setCharacterStream(int arg0, Reader arg1, long arg2) throws SQLException {
		
		
	}
	
	@Override
	public void setClob(int arg0, Reader arg1) throws SQLException {
		
		
	}
	
	@Override
	public void setClob(int arg0, Reader arg1, long arg2) throws SQLException {
		
		
	}
	
	@Override
	public void setNCharacterStream(int arg0, Reader arg1) throws SQLException {
		
		
	}
	
	@Override
	public void setNCharacterStream(int arg0, Reader arg1, long arg2) throws SQLException {
		
		
	}
	
	@Override
	public void setNClob(int arg0, NClob arg1) throws SQLException {
		
		
	}
	
	@Override
	public void setNClob(int arg0, Reader arg1) throws SQLException {
		
		
	}
	
	@Override
	public void setNClob(int arg0, Reader arg1, long arg2) throws SQLException {
		
		
	}
	
	@Override
	public void setNString(int arg0, String arg1) throws SQLException {
		
		
	}
	
	@Override
	public void setRowId(int arg0, RowId arg1) throws SQLException {
		
		
	}
	
	@Override
	public void setSQLXML(int arg0, SQLXML arg1) throws SQLException {
		
		
	}
	
	@Override
	public void closeOnCompletion() throws SQLException {
		
		
	}
	
	@Override
	public boolean isCloseOnCompletion() throws SQLException {
		
		return false;
	}
	
	@Override
	public boolean isClosed() throws SQLException {
		
		return false;
	}
	
	@Override
	public boolean isPoolable() throws SQLException {
		
		return false;
	}
	
	@Override
	public void setPoolable(boolean arg0) throws SQLException {
		
		
	}
	
	@Override
	public boolean isWrapperFor(Class<?> arg0) throws SQLException {
		
		return false;
	}
	
	@Override
	public <T> T unwrap(Class<T> arg0) throws SQLException {
		
		return null;
	}
	
	@Override
	public Reader getCharacterStream(int parameterIndex) throws SQLException {
		
		return null;
	}
	
	@Override
	public Reader getCharacterStream(String parameterName) throws SQLException {
		
		return null;
	}
	
	@Override
	public Reader getNCharacterStream(int parameterIndex) throws SQLException {
		
		return null;
	}
	
	@Override
	public Reader getNCharacterStream(String parameterName) throws SQLException {
		
		return null;
	}
	
	@Override
	public NClob getNClob(int parameterIndex) throws SQLException {
		
		return null;
	}
	
	@Override
	public NClob getNClob(String parameterName) throws SQLException {
		
		return null;
	}
	
	@Override
	public String getNString(int parameterIndex) throws SQLException {
		
		return null;
	}
	
	@Override
	public String getNString(String parameterName) throws SQLException {
		
		return null;
	}
	
	@Override
	public <T> T getObject(int parameterIndex, Class<T> type) throws SQLException {
		
		return null;
	}
	
	@Override
	public <T> T getObject(String parameterName, Class<T> type) throws SQLException {
		
		return null;
	}
	
	@Override
	public RowId getRowId(int parameterIndex) throws SQLException {
		
		return null;
	}
	
	@Override
	public RowId getRowId(String parameterName) throws SQLException {
		
		return null;
	}
	
	@Override
	public SQLXML getSQLXML(int parameterIndex) throws SQLException {
		
		return null;
	}
	
	@Override
	public SQLXML getSQLXML(String parameterName) throws SQLException {
		
		return null;
	}
	
	@Override
	public void setAsciiStream(String parameterName, InputStream x) throws SQLException {
		
	}
	
	@Override
	public void setAsciiStream(String parameterName, InputStream x, long length) throws SQLException {
		
	}
	
	@Override
	public void setBinaryStream(String parameterName, InputStream x) throws SQLException {
		
	}
	
	@Override
	public void setBinaryStream(String parameterName, InputStream x, long length) throws SQLException {
		
	}
	
	@Override
	public void setBlob(String parameterName, Blob x) throws SQLException {
		
	}
	
	@Override
	public void setBlob(String parameterName, InputStream inputStream) throws SQLException {
		
		
	}
	
	@Override
	public void setBlob(String parameterName, InputStream inputStream, long length) throws SQLException {
		
	}
	
	@Override
	public void setCharacterStream(String parameterName, Reader reader) throws SQLException {
		
	}
	
	@Override
	public void setCharacterStream(String parameterName, Reader reader, long length) throws SQLException {
		
	}
	
	@Override
	public void setClob(String parameterName, Clob x) throws SQLException {
		
		
	}
	
	@Override
	public void setClob(String parameterName, Reader reader) throws SQLException {
		
	}
	
	@Override
	public void setClob(String parameterName, Reader reader, long length) throws SQLException {
		
	}
	
	@Override
	public void setNCharacterStream(String parameterName, Reader value) throws SQLException {
		
	}
	
	@Override
	public void setNCharacterStream(String parameterName, Reader value, long length) throws SQLException {
		
	}
	
	@Override
	public void setNClob(String parameterName, NClob value) throws SQLException {
		
	}
	
	@Override
	public void setNClob(String parameterName, Reader reader) throws SQLException {
		
	}
	
	@Override
	public void setNClob(String parameterName, Reader reader, long length) throws SQLException {
		
	}
	
	@Override
	public void setNString(String parameterName, String value) throws SQLException {
		
	}
	
	@Override
	public void setRowId(String parameterName, RowId x) throws SQLException {

	}
	
	@Override
	public void setSQLXML(String parameterName, SQLXML xmlObject) throws SQLException {
		
	}

}
