package org.easitline.common.db;


import java.util.Map;

import com.alibaba.fastjson.JSONObject;


public interface EasyRow   extends java.io.Serializable {
  /**
   * 根据列名取得列的值
   * @param columnName 列名
   * @return  列值
   */
  public String getColumnValue(String columnName);
  
/**
 * 根据列名取得列的值
 * @param columnName
 * @param defaultValue
 * @return
 */
  public String getColumnValue(String columnName,String defaultValue);

  /**
   * 根据列名取得列的值
   * @param columnName 列名
   * @return  列值
   */
  public String getColumnValue(int columnIndex);
  
  
  /**
   * 获得列值
   * @param columnIndex
   * @param defaultValue
   * @return
   */
  public String getColumnValue(int columnIndex,String defaultValue);

  /**
   * 取得列名
   * @param columnIndex int  列下标
   * @return String 列名
   */
  public String getColumnName(int columnIndex);

  /**
   * 取得列名
   * @param columnIndex int
   * @return String[]
   */
  public String[] getColumnNames();

  /**
   * 取得列数
   * @return int  列数量
   */
  public int getColumnCount();
  
  /**
   * 转化成Map对象
   * @return
   */
  public JSONObject  toJSONObject();

  /**
   * 把对象转换成MAP
   * @return
   */
  public Map<String,String> toMap();


}
