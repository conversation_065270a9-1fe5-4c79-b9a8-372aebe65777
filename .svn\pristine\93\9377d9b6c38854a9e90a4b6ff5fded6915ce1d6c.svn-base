<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.yunqu.mars</groupId>
        <artifactId>mars-all</artifactId>
        <version>3.5</version>
    </parent>
    
    <groupId>com.yunqu.mars</groupId>
    <artifactId>easitline-db</artifactId>
    <version>3.5</version>
    <packaging>jar</packaging>
    
    <name>easitline-db</name>
    <url>https://www.yunqu-info.com</url>
    <description>Mars开发DB包,提供增删改查方法</description>
  
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.source.plugin.version>3.3.0</maven.source.plugin.version>
        <java.version>1.8</java.version>
    </properties>
    
    <dependencies>
    	<dependency>
    		<groupId>com.yunqu.mars</groupId>
    		<artifactId>easitline-db</artifactId>
    		<version>3.4</version>
		    <scope>provided</scope>
    	</dependency>
		<dependency>
		    <groupId>com.jfinal</groupId>
		    <artifactId>enjoy</artifactId>
		    <version>5.2.2</version>
		    <scope>provided</scope>
		</dependency>
    </dependencies>
  

    <build>
        <sourceDirectory>src</sourceDirectory>
        <resources>
	        <resource>
	            <directory>src</directory>
	            <includes>
	                <include>**/*</include>
	            </includes>
	        </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven.source.plugin.version}</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>