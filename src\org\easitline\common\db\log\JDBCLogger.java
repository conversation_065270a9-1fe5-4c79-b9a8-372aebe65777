package org.easitline.common.db.log;



import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
  
/**
 * Easitline 操作日志类
 * <AUTHOR>
 *
 */
public class JDBCLogger  {

	private static final   String APP_NAME = "easitline-jdbc";
	
	private static final   String LOG_NAME = "easitline-jdbc";
	
	public static Logger getLogger(){
		return LogEngine.getLogger(APP_NAME,LOG_NAME);
	}
}
