package org.easitline.common.db;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.easitline.common.db.impl.EasyQueryImpl;

/**
 * Mars 数据库统一操作接口
EasyQuery  easyQuery = EasyQuery.getQuery("应用ID","应用数据源");

//判断记录是否存在
String sql = "select count(1) as tcount from EASI_USER where USER_ID = ?";
boolean isExist = easyQuery.queryForExist(sql,new Object[]{"admin"});

//获得单个值
String sql = "select USERNAME from EASI_USER where USER_ID = ?";
String  username = easyQuery.queryForString(sql,new Object[]{"admin"});

//获得查询列表
String sql = "select * from EASI_USER ";
List<EasyRow> list = easyQuery.queryForString(sql,null);
*/
public abstract class EasyQuery {
	
	/**
	 * 获取查询对象
	 * @param driverName  driver name
	 * @param url   url
	 * @param dbuser  db user
	 * @param password  dbpassword
	 * @return
	 */

	public static EasyQuery getQuery(String driverName,String url,String dbuser,String password) {
		 return new EasyQueryImpl(url,dbuser,password);
	}
	
	/**
	 * 获得查询对象
	 * @param appName  应用名
	 * @param appDatasourceName  应用数据源
	 * @return
	 */
	public static EasyQuery getQuery(String appName,String appDatasourceName){
		return new EasyQueryImpl(appName,appDatasourceName);
	}
	
	public static EasyQuery getQuery(String appName,String appDatasourceName,Logger logger){
		EasyQuery easyQuery=new EasyQueryImpl(appName,appDatasourceName);
		easyQuery.setLogger(logger);
		return easyQuery;
	}
	
	/**
	 * 获得Db对象
	 * @param appName  应用名
	 * @param appDatasourceName  应用数据源
	 * @return
	 */
	public static EasyQuery getDb(String appName,String appDatasourceName){
		return  getQuery(appName,appDatasourceName);
		
	}
	
	/**
	 * 获得查询对象
	 * @param sysDatasourceName 系统数据源
	 * @return
	 */
	public static EasyQuery getQuery(String sysDatasourceName){
		return new EasyQueryImpl(sysDatasourceName);
	}
	
	
	/**
	 * 获得查询对象 ，事务和链接的关闭由外部来实现，EasyQuery中不处理关闭链接。
	 * @param conn 外部链接
	 * @return
	 */
	public static EasyQuery getQuery(Connection conn){
		return new EasyQueryImpl(conn);
	}
	
	public abstract Connection getConnection() throws SQLException ;
	
	/**
	 * 获得当前数据类型
	 * @return
	 */
	public abstract DBTypes  getTypes();
	
	/**
	 * 判断是否有查询结果
	 * 
	 * @param sql
	 *            select count(*) from table
	 * @return true or false;
	 */
	public abstract boolean queryForExist(String sql,Object... args) throws SQLException;

	/**
	 * 获得查询返回的一个Int值。
	 * 
	 * @param sql
	 *            select intColumn  from table  where ...
	 * @return  一个INT值
	 */
	public abstract int queryForInt(String sql,Object... args) throws SQLException;
	
	/**
	 * 取得查询只有唯一值的返回值
	 * 
	 * @param sql
	 *            select columnfrom table where tableid =
	 * @return String;
	 */

	public abstract String queryForString(String sql,Object... params) throws SQLException;

	/**
	 * 执行select相关的查询，返回查询结果。
	 * 
	 * @param sql
	 *            select * from tablename .... ,列名必须存在
	 * @return ArrayList->RowModel
	 * @throws SQLException
	 *             操作过程出现异常
	 * @return ArrayList->RowModel
	 */

	public abstract List<EasyRow> queryForList(String sql,Object... params) throws SQLException;
	

	
	/**
	 * 执行查询，并返回结果
	 * @param sql 查询SQL
	 * @param params 查询参数
	 * @param rowMapper  转换对象
	 * @return
	 * @throws SQLException
	 */
	public abstract <T> List<T> queryForList(String sql,Object[] params,EasyRowMapper<T> rowMapper) throws SQLException;
	
	/**
	 * 执行select相关的查询，返回查询结果。
	 * 
	 * @param sql
	 *            select * from tablename .... ,列名必须存在
	 * @return ArrayList->RowModel
	 * @throws SQLException
	 *             操作过程出现异常
	 * @return ArrayList->RowModel
	 */

	public abstract List<EasyRow> queryForList(String sql,Object[] params,int pageNum,int pageSize) throws SQLException;
	
	
	/**
	 * 执行查询，并返回结果
	 * @param sql 查询SQL
	 * @param params  查询参数
	 * @param pageNum  页码
	 * @param pageSize   每页大小
	 * @param rowMapper  转换对象
	 * @return
	 * @throws SQLException
	 */
	
	public abstract <T> List<T> queryForList(String sql,Object[] params,int pageNum,int pageSize,EasyRowMapper<T> rowMapper) throws SQLException;

	/**
	 * 取得查询返回的第一行
	 * 
	 * @param sql
	 *            用于进行记录修改时用，select * from tablename where tableid = id
	 * @return EasyRow
	 */

	public abstract EasyRow queryForRow(String sql,Object... params) throws SQLException;
	

	
	/**
	 * 获取查询的第一行，并返回指定的对象信息
	 * @param sql  查询SQL
	 * @param params  参数对象
	 * @param rowMapper  转换器
	 * @return  自定义对象
	 * @throws SQLException
	 */
	public abstract <T> T queryForRow(String sql,Object[] params,EasyRowMapper<T> rowMapper ) throws SQLException;
	
	
	/**
	 * 执行 update 、delete、create的等批操作
	 * 
	 * @param sqlList
	 *            ArrayList -> sql 保存每一条要执行的sql语句
	 * @throws SQLException
	 *             执行失败抛出异常
	 */

	public abstract void execute(String sql,Object... args) throws SQLException;
	
	/**
	 * 删除数据
	 * @param record  需对record对象primaryValues设值
	 * @return
	 * @throws SQLException
	 */
	public abstract  boolean deleteById(EasyRecord record) throws SQLException;
	/**
	 * 新增数据
	 * @param record
	 * @return
	 * @throws SQLException
	 */
	public abstract  boolean save(EasyRecord record) throws SQLException;
	/**
	 * 执行修改
	 * @param record
	 * @return
	 * @throws SQLException
	 */
	public abstract boolean update(EasyRecord record) throws SQLException;
	/***
	 * 通过表Id查询指定列一条数据
	 * @param record对象 需对record对象primaryValues设值
	 * @param columns 列名称 支持多列（userId,userName）
	 * @return Map对象
	 * @throws SQLException
	 */
	public abstract Map<String,String> findById(EasyRecord record,String columns) throws SQLException;
	/***
	 * 通过表Id查询整条记录所有字段数据
	 * @param record对象 需对record对象primaryValues设值
	 * @return Map对象
	 * @throws SQLException
	 */
	public abstract Map<String,String> findById(EasyRecord record) throws SQLException;
	

	/**
	 * 执行 update 、delete、create的等批操作
	 * 
	 * @param sqlList
	 *            ArrayList -> sql 保存每一条要执行的sql语句
	 * @throws SQLException
	 *             执行失败抛出异常
	 */

	public abstract int executeUpdate(String sql,Object... args) throws SQLException;
	
	

	/**
	 * 执行sql的预查询过程
	 * 
	 * @param sql
	 *            String
	 * @return PreparedStatement
	 */
	public abstract EasyPreparedStatement preparedStatement(String sql) throws SQLException;
	
	/**
	 * 执行批量SQL
	 * @param sqlList
	 * @return
	 * @throws SQLException
	 */
	public abstract int[] executeBatch(List<String> sqlList) throws SQLException;
	
	
	
	/**
	 * 执行批量SQL
	 * @param sqlList
	 * @return
	 * @throws SQLException
	 */
	public abstract int[] executeBatch(String sql,List<Object[]> params) throws SQLException;

	/**
	 * 执行存储过程
	 * 
	 * @param sql
	 *            String
	 * @return PreparedStatement
	 */

	public abstract EasyCallableStatement callableStatement(String sql) throws SQLException;

	/**
	 * 设置最大的查询返回记录数
	 * 
	 * @param count
	 * int
	 */

	public abstract void setMaxRow(int count);
	
	/**
	 * 设置分页语句
	 * @param pageSql
	 */
	public abstract void setPageSql(String pageSql);
	
	/**
	 * 设置分页是否启用子查询方式 默认启用
	 * @param subQuery
	 * @return
	 */
	public abstract void setSubQuery(boolean subQuery);
	
	/**
	 * 设置最大查询超时时间 默认10秒
	 * @param timeout
	 */
	public abstract void setTimeout(int timeout);
	
	public abstract void setLogger(Logger logger);
	
	/**
     * 字段大小写转换,默认 2
     * 0 不转换 ,1 转小写, 2转大写
     */
	public abstract void setConvertField(int convertField);
	
    public abstract void setShowFullSql(boolean showFullSql);
    
    /**
             * 设置表前缀
     * @param schameName
     */
    public abstract void setSchameName(String schameName);

	/**
	 * 开始新的事务
	 * 
	 * @throws SQLException
	 */
	public abstract void begin() throws SQLException;

	/**
	 * 回滚事务
	 * 
	 * @throws SQLException
	 */
	public abstract void rollback() throws SQLException;
	
	/**
	 * 拼接错误但是又不能删 改用rollback
	 * @throws SQLException
	 */
	public abstract void roolback() throws SQLException;

	/**
	 * 提交事务
	 * 
	 * @throws SQLException
	 */
	public abstract void commit() throws SQLException;


}
